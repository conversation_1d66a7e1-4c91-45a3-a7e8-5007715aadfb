
### Problem Analysis

#### 1. Data Consistency & Concurrency Problems

- **Issue 1-A: Lost-update / Double-debit Race Conditions**
  - The code uses read-then-modify-then-save pattern (`find_by_id` → `save`)
  - Two concurrent transfers can read the same balance simultaneously, leading to overdrafts
  - No atomic operations ensure balance consistency


- **Issue 1-B: Half-completed Transfer**
  - Source account is debited and saved before destination account is credited
  - System crash between the two save operations permanently loses money
  - No transactional integrity across the full transfer operation

- **Issue 1-C: Deadlock Probability**
  - Accounts are locked in arbitrary order (source first, destination second)
  - Concurrent transfers between the same accounts in opposite directions can deadlock
  - No deterministic locking strategy

- **Fixes:**
  - Wrap the whole transfer in a single database transaction with SERIALIZABLE isolation level
  - Lock rows in deterministic order (ORDER BY account_id) to prevent cyclical waits
  - Use pessimistic row locking (SELECT ... FOR UPDATE) so both balances are changed atomically
  - Move the debit + credit into one transaction scope ensuring atomic commit/rollback


#### 2. Idempotency & Message Delivery

- **Issue 2: Duplicate Processing**
  - No protection against duplicate transfer requests
  - Client retries (e.g., HTTP 504 timeouts) can execute the same transfer multiple times
  - No unique transfer identification mechanism

- **Fix:**
  - Require a "transfer-id" or "idempotency-key" and store it with a unique constraint
  - Reject duplicates at the DB layer before processing begins
  - Return the same result for duplicate requests to maintain idempotent behavior

#### 3. Precision & Monetary Correctness

- **Issue 3: Decimal Scale Drift**
  - Uses `Decimal` but never quantizes amounts
  - Arithmetic operations can introduce 28-decimal-place precision drift
  - No standardized monetary precision enforcement


- **Fix:**
  - Define a CENT = Decimal("0.01") constant and quantize all monetary values to two places
  - Apply quantization before every calculation to maintain consistent precision
  - Use ROUND_HALF_UP for standard banking rounding behavior

#### 4. Time & Auditing

- **Issue 4: Naive Timestamps**
  - `datetime.now()` returns timezone-naive objects
  - Multi-server deployments across time zones create ambiguous audit trails
  - No consistent temporal ordering of events

- **Issue 5: Poor Audit Trail**
  - Logs only free-form strings without structured data
  - Missing critical audit fields: transfer_id, correlation_id, duration
  - Difficult to trace transactions for compliance and debugging

- **Fixes:**
  - Use `datetime.now(tz=timezone.utc)
  - Enrich structured logs (JSON or key-value) with transfer_id, from_account_id, to_account_id, amount, status, duration_ms
  - Store transfer records in database for permanent audit trail with proper indexing

#### 5. Exception Handling & API Contract

- **Issue 6: Swallowing Root Cause**
  - Catches all exceptions and returns boolean
  - Callers must parse logs to understand failure reasons
  - No domain-specific error types for different failure scenarios

- **Fix:**
  - Raise domain-specific exceptions (InsufficientFundsError, AccountNotFoundError, TransferFailedError)
  - Make the API explicit so tests can assert on specific error conditions
  - Preserve exception context while wrapping system errors in domain exceptions

#### 6. Design Hygiene (Clean Code Alignment)

- **Issue 7: Mixed Responsibilities**
  - Service handles both business logic and persistence orchestration
  - Creates its own logger instead of dependency injection
  - Violates Single Responsibility Principle

- **Fix:**
  - Keep Single Responsibility Principle: let the repository manage transactions, let the service express only business rules
  - Inject the logger (logger: logging.Logger) for better testability
  - Separate transaction management from business logic using context managers



### Implementation


```python
#!/usr/bin/env python3
"""
Money transfer service implementation for Question 5.

This module demonstrates a clean solution addressing the critical issues
identified in the original code while maintaining appropriate scope for a home test.
"""

import logging
from datetime import datetime, timezone
from decimal import Decimal, ROUND_HALF_UP
from uuid import uuid4
from typing import Optional
from dataclasses import dataclass

# Monetary precision constant
CENT = Decimal("0.01")

# Domain-specific exceptions
class TransferError(Exception):
    """Base exception for transfer operations."""
    pass

class AccountNotFound(TransferError):
    """Raised when an account cannot be found."""
    pass

class InsufficientFunds(TransferError):
    """Raised when source account has insufficient balance."""
    pass

class DuplicateTransfer(TransferError):
    """Raised when attempting to process a duplicate transfer."""
    pass

@dataclass
class Account:
    """Account entity with balance and metadata."""
    id: str
    balance: Decimal
    last_updated: datetime

class TransferService:
    """Money transfer service with proper error handling and transaction safety."""

    def __init__(self, account_repository):
        self.account_repository = account_repository
        self.logger = logging.getLogger(__name__)

    def transfer_money(
        self,
        from_account_id: str,
        to_account_id: str,
        raw_amount: Decimal,
        transfer_id: Optional[str] = None
    ) -> bool:
        """Transfer money between accounts with proper error handling and atomicity."""
        # Generate transfer ID if not provided for idempotency
        if not transfer_id:
            transfer_id = str(uuid4())

        # Quantize amount to prevent precision issues
        amount = raw_amount.quantize(CENT, ROUND_HALF_UP)

        try:
            # Start database transaction for atomicity
            with self.account_repository.transaction():

                # Check for duplicate transfer (idempotency)
                if hasattr(self.account_repository, 'transfer_exists') and \
                   self.account_repository.transfer_exists(transfer_id):
                    raise DuplicateTransfer(f"Transfer {transfer_id} already processed")

                # Find and validate accounts exist
                source_account = self.account_repository.find_by_id(from_account_id)
                if not source_account:
                    raise AccountNotFound(f"Source account not found: {from_account_id}")

                destination_account = self.account_repository.find_by_id(to_account_id)
                if not destination_account:
                    raise AccountNotFound(f"Destination account not found: {to_account_id}")

                # Validate sufficient funds
                if source_account.balance < amount:
                    raise InsufficientFunds(
                        f"Insufficient funds in account {from_account_id}"
                    )

                # Execute transfer with UTC timestamp (fix timezone issue)
                timestamp = datetime.now(tz=timezone.utc)

                # Update balances atomically
                source_account.balance = source_account.balance - amount
                source_account.last_updated = timestamp
                destination_account.balance = destination_account.balance + amount
                destination_account.last_updated = timestamp

                # Save changes within transaction
                self.account_repository.save(source_account)
                self.account_repository.save(destination_account)

                # Record transfer for audit trail and idempotency (if supported)
                if hasattr(self.account_repository, 'record_transfer'):
                    self.account_repository.record_transfer(
                        transfer_id, from_account_id, to_account_id, amount, timestamp
                    )

            # Log successful transfer
            self.logger.info(
                f"Transfer of {amount} from account {from_account_id} to {to_account_id} completed successfully"
            )
            return True

        except TransferError as e:
            # Log business errors and re-raise
            self.logger.error(f"Transfer failed: {str(e)}")
            raise
        except Exception as e:
            # Log system errors and wrap in domain exception
            self.logger.error(f"Error during transfer: {str(e)}", exc_info=True)
            raise TransferError("Transfer failed due to system error") from e


```